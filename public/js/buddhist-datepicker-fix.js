/**
 * Buddhist DatePicker Fix - Standalone JavaScript
 * แก้ปัญหาการแปลงปี พ.ศ. ใน Date Picker ทุก mode
 * ใช้งานได้กับ jQ<PERSON><PERSON>trap DateTimePicker และ date picker อื่นๆ
 * 
 * การใช้งาน:
 * 1. เพิ่ม script tag ใน HTML
 * 2. เรียก BuddhistDatePickerFix.init()
 */

(function(window) {
    'use strict';

    // Buddhist Era utilities
    const BuddhistUtils = {
        // แปลง ค.ศ. เป็น พ.ศ.
        toBuddhistYear: function(gregorianYear) {
            return gregorianYear + 543;
        },

        // แปลง พ.ศ. เป็น ค.ศ.
        toGregorianYear: function(buddhistYear) {
            return buddhistYear - 543;
        },

        // ตรวจสอบว่าเป็นปี ค.ศ. หรือไม่
        isGregorianYear: function(year) {
            return year >= 1900 && year <= 2100;
        },

        // ตรวจสอบว่าเป็นปี พ.ศ. หรือไม่
        isBuddhistYear: function(year) {
            return year >= 2443 && year <= 2643;
        },

        // แปลงปีในข้อความ
        convertYearsInText: function(text) {
            return text
                // แปลงปีเดี่ยว
                .replace(/\b(\d{4})\b/g, function(match, year) {
                    var yearNum = parseInt(year, 10);
                    if (BuddhistUtils.isGregorianYear(yearNum)) {
                        return BuddhistUtils.toBuddhistYear(yearNum).toString();
                    }
                    return match;
                })
                // แปลงช่วงปี
                .replace(/(\d{4})\s*[-–—]\s*(\d{4})/g, function(match, startYear, endYear) {
                    var start = parseInt(startYear, 10);
                    var end = parseInt(endYear, 10);
                    
                    if (BuddhistUtils.isGregorianYear(start) && BuddhistUtils.isGregorianYear(end)) {
                        return BuddhistUtils.toBuddhistYear(start) + '-' + BuddhistUtils.toBuddhistYear(end);
                    }
                    return match;
                });
        }
    };

    // Main Buddhist DatePicker Fix
    const BuddhistDatePickerFix = {
        isActive: false,
        observer: null,
        options: {
            debug: false,
            delay: 50,
            selectors: [
                // Bootstrap DateTimePicker
                '.bootstrap-datetimepicker-widget .year',
                '.bootstrap-datetimepicker-widget .decade',
                '.bootstrap-datetimepicker-widget .picker-switch',
                '.bootstrap-datetimepicker-widget th',
                '.bootstrap-datetimepicker-widget .datepicker-years .year',
                '.bootstrap-datetimepicker-widget .datepicker-decades .decade',
                
                // Generic selectors
                '.datepicker-years .year',
                '.datepicker-decades .decade',
                '.datepicker-months th',
                '.datepicker-days th',
                '.year-picker .year',
                '.month-picker .year'
            ]
        },

        // เริ่มต้นการทำงาน
        init: function(customOptions) {
            if (this.isActive) {
                this.log('Already active');
                return;
            }

            // รวม options
            if (customOptions) {
                for (var key in customOptions) {
                    if (customOptions.hasOwnProperty(key)) {
                        this.options[key] = customOptions[key];
                    }
                }
            }

            this.isActive = true;
            this.setupMomentLocale();
            this.setupDatePickerEvents();
            this.setupMutationObserver();
            this.convertAllElements();
            this.log('Buddhist DatePicker Fix initialized');
        },

        // หยุดการทำงาน
        stop: function() {
            if (!this.isActive) {
                this.log('Already stopped');
                return;
            }

            this.isActive = false;
            this.cleanup();
            this.log('Buddhist DatePicker Fix stopped');
        },

        // ตั้งค่า moment.js locale
        setupMomentLocale: function() {
            if (typeof moment === 'undefined') {
                this.log('Moment.js not found');
                return;
            }

            moment.defineLocale('th-buddhist-fixed', {
                months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),
                monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),
                weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),
                weekdaysShort: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),
                weekdaysMin: 'อา_จ_อ_พ_พฤ_ศ_ส'.split('_'),
                longDateFormat: {
                    LT: 'H:mm',
                    LTS: 'H:mm:ss',
                    L: 'DD/MM/YYYY',
                    LL: 'D MMMM YYYY',
                    LLL: 'D MMMM YYYY เวลา H:mm',
                    LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'
                },
                preparse: function(string) {
                    return string.replace(/\b(\d{4})\b/g, function(match, year) {
                        var yearNum = parseInt(year, 10);
                        if (BuddhistUtils.isBuddhistYear(yearNum)) {
                            return BuddhistUtils.toGregorianYear(yearNum).toString();
                        }
                        return match;
                    });
                },
                postformat: function(string) {
                    return string.replace(/\b(\d{4})\b/g, function(match, year) {
                        var yearNum = parseInt(year, 10);
                        if (BuddhistUtils.isGregorianYear(yearNum)) {
                            return BuddhistUtils.toBuddhistYear(yearNum).toString();
                        }
                        return match;
                    });
                }
            });

            this.log('Moment.js locale setup complete');
        },

        // ตั้งค่า event listeners
        setupDatePickerEvents: function() {
            if (typeof $ === 'undefined') {
                this.log('jQuery not found');
                return;
            }

            var self = this;
            var convertElements = function() {
                setTimeout(function() {
                    self.convertAllElements();
                }, self.options.delay);
            };

            // Event listeners สำหรับ Bootstrap DateTimePicker
            $(document).on('dp.show dp.update dp.change dp.viewModeChange', '[data-toggle="datetimepicker"], .datetimepicker', function(e) {
                self.log('DatePicker event: ' + e.type);
                convertElements();
            });

            this.log('DatePicker events setup complete');
        },

        // ตั้งค่า MutationObserver
        setupMutationObserver: function() {
            if (typeof MutationObserver === 'undefined') {
                this.log('MutationObserver not supported');
                return;
            }

            var self = this;
            this.observer = new MutationObserver(function(mutations) {
                var shouldConvert = false;

                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (self.isDatePickerElement(node)) {
                                    shouldConvert = true;
                                    self.log('DatePicker element added: ' + node.className);
                                    break;
                                }
                            }
                        }
                    }

                    if (mutation.type === 'characterData') {
                        var parentElement = mutation.target.parentElement;
                        if (parentElement && self.isDatePickerElement(parentElement)) {
                            shouldConvert = true;
                            self.log('Text content changed in DatePicker');
                        }
                    }
                });

                if (shouldConvert) {
                    setTimeout(function() {
                        self.convertAllElements();
                    }, self.options.delay);
                }
            });

            this.observer.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: true
            });

            this.log('MutationObserver setup complete');
        },

        // แปลงทุก elements
        convertAllElements: function() {
            if (!this.isActive) return;

            var self = this;
            this.options.selectors.forEach(function(selector) {
                var elements = document.querySelectorAll(selector);
                for (var i = 0; i < elements.length; i++) {
                    self.convertElement(elements[i]);
                }
            });
        },

        // แปลง element เดียว
        convertElement: function(element) {
            if (!element || element.hasAttribute('data-buddhist-converted')) {
                return;
            }

            var originalText = element.textContent || '';
            if (!originalText.trim()) {
                return;
            }

            var convertedText = BuddhistUtils.convertYearsInText(originalText);
            
            if (convertedText !== originalText) {
                element.textContent = convertedText;
                element.setAttribute('data-buddhist-converted', 'true');
                element.setAttribute('data-buddhist-original', originalText);
                
                this.log('Converted element: ' + originalText + ' → ' + convertedText);
            }
        },

        // ตรวจสอบว่าเป็น DatePicker element หรือไม่
        isDatePickerElement: function(element) {
            var classNames = [
                'bootstrap-datetimepicker-widget',
                'datepicker-years',
                'datepicker-decades',
                'datepicker-months',
                'datepicker-days'
            ];

            for (var i = 0; i < classNames.length; i++) {
                if (element.classList && element.classList.contains(classNames[i])) {
                    return true;
                }
            }

            return element.closest && (
                element.closest('.bootstrap-datetimepicker-widget') ||
                element.closest('.datepicker-years') ||
                element.closest('.datepicker-decades') ||
                element.closest('.datepicker-months') ||
                element.closest('.datepicker-days')
            );
        },

        // รีเซ็ตการแปลง
        resetAllConversions: function() {
            var elements = document.querySelectorAll('[data-buddhist-converted]');
            for (var i = 0; i < elements.length; i++) {
                var element = elements[i];
                var original = element.getAttribute('data-buddhist-original');
                if (original) {
                    element.textContent = original;
                }
                element.removeAttribute('data-buddhist-converted');
                element.removeAttribute('data-buddhist-original');
            }
            this.log('Reset all conversions');
        },

        // ทำความสะอาด
        cleanup: function() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            if (typeof $ !== 'undefined') {
                $(document).off('dp.show dp.update dp.change dp.viewModeChange');
            }

            this.resetAllConversions();
        },

        // Log function
        log: function(message, data) {
            if (this.options.debug) {
                console.log('[BuddhistDatePickerFix] ' + message, data || '');
            }
        }
    };

    // Export to global scope
    window.BuddhistDatePickerFix = BuddhistDatePickerFix;
    window.BuddhistUtils = BuddhistUtils;

    // Auto-start เมื่อ DOM ready
    function autoStart() {
        // รอให้ moment.js และ jQuery โหลดเสร็จ
        setTimeout(function() {
            BuddhistDatePickerFix.init({ debug: false });
        }, 1500);
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoStart);
    } else {
        autoStart();
    }

})(window);
