<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buddhist DatePicker Fix - Example</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap DateTimePicker CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datetimepicker@4.17.47/build/css/bootstrap-datetimepicker.min.css">
    
    <style>
        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Buddhist DatePicker Fix - Example</h1>
        <p class="text-center text-muted">ตัวอย่างการแก้ปัญหาการแปลงปี พ.ศ. ใน Date Picker</p>

        <!-- Status Section -->
        <div class="demo-section">
            <h3>Status</h3>
            <div class="row">
                <div class="col-md-6">
                    <p>
                        <span id="status-indicator" class="status-indicator status-inactive"></span>
                        Buddhist DatePicker Fix: <span id="status-text">Inactive</span>
                    </p>
                    <button id="toggle-fix" class="btn btn-primary">Start Fix</button>
                    <button id="reset-conversions" class="btn btn-warning">Reset Conversions</button>
                </div>
                <div class="col-md-6">
                    <p><strong>Libraries Loaded:</strong></p>
                    <ul>
                        <li>jQuery: <span id="jquery-status">❌</span></li>
                        <li>Moment.js: <span id="moment-status">❌</span></li>
                        <li>Bootstrap DateTimePicker: <span id="datetimepicker-status">❌</span></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- DatePicker Examples -->
        <div class="demo-section">
            <h3>DatePicker Examples</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="datepicker1" class="form-label">Date Picker 1 (Default Locale)</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="datepicker1" placeholder="เลือกวันที่">
                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="datepicker2" class="form-label">Date Picker 2 (Buddhist Locale)</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="datepicker2" placeholder="เลือกวันที่">
                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Elements -->
        <div class="demo-section">
            <h3>Test Elements (จะถูกแปลงโดย Fix)</h3>
            <div class="row">
                <div class="col-md-4">
                    <h5>Simulated Bootstrap DateTimePicker</h5>
                    <div class="bootstrap-datetimepicker-widget" style="position: static; display: block;">
                        <div class="picker-switch">มกราคม 2024</div>
                        <div class="year">2024</div>
                        <div class="decade">2020-2029</div>
                        <th>2024</th>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Year Picker Elements</h5>
                    <div class="datepicker-years">
                        <div class="year">2023</div>
                        <div class="year">2024</div>
                        <div class="year">2025</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Decade Picker Elements</h5>
                    <div class="datepicker-decades">
                        <div class="decade">2010-2019</div>
                        <div class="decade">2020-2029</div>
                        <div class="decade">2030-2039</div>
                    </div>
                </div>
            </div>
            <button id="force-convert" class="btn btn-secondary mt-3">Force Convert Test Elements</button>
        </div>

        <!-- Utility Functions -->
        <div class="demo-section">
            <h3>Utility Functions</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Year Conversion</h5>
                    <div class="input-group mb-2">
                        <input type="number" class="form-control" id="year-input" placeholder="ใส่ปี ค.ศ." value="2024">
                        <button class="btn btn-outline-secondary" id="convert-year">Convert to พ.ศ.</button>
                    </div>
                    <div id="year-result" class="text-muted"></div>
                </div>
                <div class="col-md-6">
                    <h5>Date Formatting</h5>
                    <button class="btn btn-outline-secondary" id="format-today">Format Today's Date</button>
                    <div id="date-result" class="text-muted mt-2"></div>
                </div>
            </div>
        </div>

        <!-- Log Output -->
        <div class="demo-section">
            <h3>Debug Log</h3>
            <div id="log-output" class="log-output">
                <div class="text-muted">Debug logs will appear here...</div>
            </div>
            <button id="clear-log" class="btn btn-sm btn-outline-secondary mt-2">Clear Log</button>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Moment.js -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/locale/th.js"></script>
    
    <!-- Bootstrap DateTimePicker -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-datetimepicker@4.17.47/build/js/bootstrap-datetimepicker.min.js"></script>
    
    <!-- Font Awesome (for icons) -->
    <script src="https://kit.fontawesome.com/your-kit-id.js" crossorigin="anonymous"></script>
    
    <!-- Buddhist DatePicker Fix -->
    <script src="js/buddhist-datepicker-fix.js"></script>

    <script>
        $(document).ready(function() {
            // ตรวจสอบ libraries ที่โหลด
            $('#jquery-status').text(typeof $ !== 'undefined' ? '✅' : '❌');
            $('#moment-status').text(typeof moment !== 'undefined' ? '✅' : '❌');
            $('#datetimepicker-status').text(typeof $.fn.datetimepicker !== 'undefined' ? '✅' : '❌');

            // ตั้งค่า DatePickers
            $('#datepicker1').datetimepicker({
                locale: 'th',
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                showClear: true
            });

            $('#datepicker2').datetimepicker({
                locale: 'th-buddhist-fixed', // ใช้ locale ที่แก้ไขแล้ว
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                showClear: true
            });

            // Override console.log เพื่อแสดงใน log output
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                
                const logOutput = document.getElementById('log-output');
                const logEntry = document.createElement('div');
                logEntry.textContent = new Date().toLocaleTimeString() + ': ' + args.join(' ');
                logOutput.appendChild(logEntry);
                logOutput.scrollTop = logOutput.scrollHeight;
            };

            // หยุด auto-start และเริ่มต้นด้วย debug mode
            BuddhistDatePickerFix.stop();
            
            // ตั้งค่า UI
            let isActive = false;
            
            function updateStatus() {
                const indicator = document.getElementById('status-indicator');
                const text = document.getElementById('status-text');
                const button = document.getElementById('toggle-fix');
                
                if (isActive) {
                    indicator.className = 'status-indicator status-active';
                    text.textContent = 'Active';
                    button.textContent = 'Stop Fix';
                    button.className = 'btn btn-danger';
                } else {
                    indicator.className = 'status-indicator status-inactive';
                    text.textContent = 'Inactive';
                    button.textContent = 'Start Fix';
                    button.className = 'btn btn-primary';
                }
            }

            // Event handlers
            $('#toggle-fix').click(function() {
                if (isActive) {
                    BuddhistDatePickerFix.stop();
                    isActive = false;
                } else {
                    BuddhistDatePickerFix.init({ debug: true });
                    isActive = true;
                }
                updateStatus();
            });

            $('#reset-conversions').click(function() {
                BuddhistDatePickerFix.resetAllConversions();
            });

            $('#force-convert').click(function() {
                BuddhistDatePickerFix.convertAllElements();
            });

            $('#convert-year').click(function() {
                const year = parseInt($('#year-input').val());
                if (year) {
                    const buddhist = BuddhistUtils.toBuddhistYear(year);
                    const back = BuddhistUtils.toGregorianYear(buddhist);
                    $('#year-result').text(`${year} ค.ศ. = ${buddhist} พ.ศ. = ${back} ค.ศ.`);
                }
            });

            $('#format-today').click(function() {
                const today = new Date();
                const day = today.getDate().toString().padStart(2, '0');
                const month = (today.getMonth() + 1).toString().padStart(2, '0');
                const year = today.getFullYear();
                const buddhist = BuddhistUtils.toBuddhistYear(year);
                
                $('#date-result').html(`
                    <div>วันนี้: ${day}/${month}/${year} (ค.ศ.)</div>
                    <div>วันนี้: ${day}/${month}/${buddhist} (พ.ศ.)</div>
                `);
            });

            $('#clear-log').click(function() {
                $('#log-output').html('<div class="text-muted">Debug logs will appear here...</div>');
            });

            updateStatus();
            
            // เริ่มต้นด้วย fix ที่เปิดอยู่
            setTimeout(function() {
                $('#toggle-fix').click();
            }, 1000);
        });
    </script>
</body>
</html>
