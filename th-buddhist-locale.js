!function (e, _) {
    "object" == typeof exports && "undefined" != typeof module && "function" == typeof require
        ? _(require("moment"))
        : "function" == typeof define && define.amd
            ? define(["moment"], _)
            : _(e.moment);
}(this, function (moment) {
    "use strict";

    // -----------------------
    // กำหนด locale ภาษาไทย
    // -----------------------
    moment.defineLocale("th", {
        months: "มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),
        monthsShort: "ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),
        monthsParseExact: true,
        weekdays: "อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),
        weekdaysShort: "อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),
        weekdaysMin: "อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),
        weekdaysParseExact: true,
        longDateFormat: {
            LT: "H:mm",
            LTS: "H:mm:ss",
            L: "DD/MM/YYYY",
            LL: "D MMMM YYYY",
            LLL: "D MMMM YYYY เวลา H:mm",
            LLLL: "วันddddที่ D MMMM YYYY เวลา H:mm"
        },
        meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,
        isPM: h => h === "หลังเที่ยง",
        meridiem: h => (h < 12 ? "ก่อนเที่ยง" : "หลังเที่ยง"),
        calendar: {
            sameDay: "[วันนี้ เวลา] LT",
            nextDay: "[พรุ่งนี้ เวลา] LT",
            nextWeek: "dddd[หน้า เวลา] LT",
            lastDay: "[เมื่อวานนี้ เวลา] LT",
            lastWeek: "[วัน]dddd[ที่แล้ว เวลา] LT",
            sameElse: "L"
        },
        relativeTime: {
            future: "อีก %s",
            past: "%sที่แล้ว",
            s: "ไม่กี่วินาที",
            ss: "%d วินาที",
            m: "1 นาที",
            mm: "%d นาที",
            h: "1 ชั่วโมง",
            hh: "%d ชั่วโมง",
            d: "1 วัน",
            dd: "%d วัน",
            w: "1 สัปดาห์",
            ww: "%d สัปดาห์",
            M: "1 เดือน",
            MM: "%d เดือน",
            y: "1 ปี",
            yy: "%d ปี"
        },
        // แปลง พ.ศ. เป็น ค.ศ. เมื่อ parse
        preparse: function (string) {
            return string.replace(/\b(\d{4})\b/g, function (match, year) {
                return parseInt(year) - 543;
            });
        },
        // แปลง ค.ศ. เป็น พ.ศ. เมื่อแสดงผล
        postformat: function (string) {
            return string.replace(/\b(\d{4})\b/g, function (match, year) {
                return parseInt(year) ;
            });
        }
    });

    // -----------------------
    // ฟังก์ชันแปลงปีใน DatePicker Widget
    // -----------------------
    
    // ฟังก์ชันแปลงทุกจุดใน datepicker
    function convertAllDatePickerYears() {
        // แปลงทุก element ที่อาจมีปี - เพิ่ม selectors ให้ครอบคลุมมากขึ้น
        const selectors = [
            // Bootstrap DateTimePicker selectors - ครอบคลุมทุก view mode
            '.bootstrap-datetimepicker-widget .year',           // ปีใน year view
            '.bootstrap-datetimepicker-widget .decade',         // ทศวรรษใน decade view
            '.bootstrap-datetimepicker-widget .picker-switch',  // หัวข้อ (เดือน ปี) - สำคัญมาก!
            '.bootstrap-datetimepicker-widget th.picker-switch', // หัวข้อแบบ th element
            '.bootstrap-datetimepicker-widget .datepicker-switch', // หัวข้อแบบอื่น
            '.bootstrap-datetimepicker-widget th',              // หัวตาราง
            '.bootstrap-datetimepicker-widget .datepicker-years .year',     // ปีใน years view (nested)
            '.bootstrap-datetimepicker-widget .datepicker-decades .decade', // ทศวรรษใน decades view (nested)
            '.bootstrap-datetimepicker-widget .datepicker-months th',       // หัวข้อใน months view (nested)
            '.bootstrap-datetimepicker-widget .datepicker-months .picker-switch', // title ใน months view
            '.bootstrap-datetimepicker-widget .datepicker-days th',         // หัวข้อใน days view (nested)
            '.bootstrap-datetimepicker-widget .datepicker-days .picker-switch', // title ใน days view

            // Generic datepicker selectors
            '.datepicker-years .year',                          // ปีใน years view
            '.datepicker-decades .decade',                      // ทศวรรษใน decades view
            '.datepicker-months th',                            // หัวข้อใน months view
            '.datepicker-months .picker-switch',                // title ใน months view
            '.datepicker-days th',                              // หัวข้อใน days view
            '.datepicker-days .picker-switch',                  // title ใน days view

            // Additional selectors for different datepicker implementations
            '.year-picker .year',                               // ปีใน year picker
            '.month-picker .year',                              // ปีใน month picker header
            '.date-picker-header',                              // header ของ date picker
            '.picker-title',                                    // title ของ picker
            '.calendar-header',                                 // header ของ calendar

            // เพิ่ม selectors สำหรับ title ที่อาจหลุด
            'th.datepicker-switch',                             // title switch
            '.datepicker-switch',                               // title switch แบบอื่น
            'thead th.picker-switch',                           // title ใน thead
            'thead .picker-switch'                              // title ใน thead แบบอื่น
        ];

        selectors.forEach(function(selector) {
            document.querySelectorAll(selector).forEach(function(el) {
                // ป้องกันการแปลงซ้ำ - ใช้ getAttribute แทน dataset
                if (el.getAttribute('data-buddhist-converted')) return;

                // เก็บข้อความเดิมไว้สำหรับ debug
                var originalText = el.textContent;
                if (!originalText || !originalText.trim()) return;

                // เก็บข้อความเดิมไว้ใน attribute
                if (!el.getAttribute('data-buddhist-original')) {
                    el.setAttribute('data-buddhist-original', originalText);
                }

                var text = originalText;

                // แปลงปีเดี่ยว - ตรวจสอบว่าเป็น ค.ศ. ก่อนแปลง
                text = text.replace(/\b(\d{4})\b/g, function(match, year) {
                    var yearNum = parseInt(year, 10);
                    // แปลงเฉพาะปี ค.ศ. (1900-2100) เป็น พ.ศ.
                    if (yearNum >= 1900 && yearNum <= 2100) {
                        return (yearNum + 543).toString();
                    }
                    // ถ้าเป็น พ.ศ. อยู่แล้ว (2443-2643) ไม่ต้องแปลง
                    return match;
                });

                // แปลงช่วงปี - ตรวจสอบว่าเป็น ค.ศ. ก่อนแปลง
                text = text.replace(/(\d{4})\s*[-–—]\s*(\d{4})/g, function(match, startYear, endYear) {
                    var start = parseInt(startYear, 10);
                    var end = parseInt(endYear, 10);

                    // แปลงเฉพาะถ้าทั้งคู่เป็น ค.ศ.
                    if (start >= 1900 && start <= 2100 && end >= 1900 && end <= 2100) {
                        return (start + 543) + '-' + (end + 543);
                    }
                    return match;
                });

                // อัพเดทข้อความถ้ามีการเปลี่ยนแปลง
                if (text !== originalText) {
                    el.textContent = text;
                    el.setAttribute('data-buddhist-converted', 'true');

                    // Debug log (ถ้าต้องการ)
                    if (typeof console !== 'undefined' && console.log) {
                        console.log('[Buddhist DatePicker] Converted:', originalText, '→', text);
                    }
                }
            });
        });
    }
    
    // ฟังก์ชันรีเซ็ตการแปลง
    function resetDatePickerConversion() {
        // รีเซ็ตการแปลงทั้งหมด - ใช้ querySelectorAll ที่ครอบคลุมมากขึ้น
        var selectors = [
            '[data-buddhist-converted]',
            '.bootstrap-datetimepicker-widget [data-buddhist-converted]',
            '.datepicker-years [data-buddhist-converted]',
            '.datepicker-decades [data-buddhist-converted]'
        ];

        selectors.forEach(function(selector) {
            document.querySelectorAll(selector).forEach(function(el) {
                // คืนค่าข้อความเดิม
                var originalText = el.getAttribute('data-buddhist-original');
                if (originalText) {
                    el.textContent = originalText;
                }
                // ลบ attributes
                el.removeAttribute('data-buddhist-converted');
                el.removeAttribute('data-buddhist-original');
            });
        });
    }
    
    // ฟังก์ชันสำหรับใช้งาน
    function initBuddhistDatePicker(selector, options) {
        options = options || {};
        
        const defaultOptions = {
            locale: 'th',
            format: 'DD/MM/YYYY'
        };
        
        // รวม options
        for (let key in options) {
            defaultOptions[key] = options[key];
        }
        
        const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
        if (!element) return null;
        
        // สร้าง datepicker (สำหรับ jQuery)
        if (typeof $ !== 'undefined' && $.fn.datetimepicker) {
            const $element = $(element);
            $element.datetimepicker(defaultOptions);
            
            // แปลงทุกครั้งที่มีการแสดงผลหรืออัพเดท
            $element.on('dp.show', function() {
                // ไม่รีเซ็ตเมื่อ show เพื่อป้องกันการหายของการแปลง
                setTimeout(convertAllDatePickerYears, 100);
                // แปลงซ้ำอีกครั้งหลังจาก DOM อัพเดท
                setTimeout(convertAllDatePickerYears, 300);
            });

            $element.on('dp.update', function() {
                setTimeout(convertAllDatePickerYears, 100);
                setTimeout(convertAllDatePickerYears, 300);
            });

            // แปลงเมื่อเปลี่ยน view mode (สำคัญมาก!)
            $element.on('dp.viewModeChange', function() {
                // รีเซ็ตเฉพาะเมื่อเปลี่ยน view mode
                setTimeout(function() {
                    resetDatePickerConversion();
                    convertAllDatePickerYears();
                }, 50);
                // แปลงซ้ำอีกครั้งเพื่อให้แน่ใจ
                setTimeout(convertAllDatePickerYears, 200);
                setTimeout(convertAllDatePickerYears, 400);
            });

            // แปลงเมื่อมีการเปลี่ยนแปลงวันที่
            $element.on('dp.change', function() {
                setTimeout(convertAllDatePickerYears, 100);
            });

            // แปลงเมื่อ hide และ show อีกครั้ง
            $element.on('dp.hide', function() {
                // ไม่รีเซ็ตเมื่อ hide เพื่อป้องกันปัญหา
                // resetDatePickerConversion();
            });
            
            return $element.data('DateTimePicker');
        }
        
        return null;
    }
    
    // ใช้ MutationObserver เพื่อตรวจจับการเปลี่ยนแปลง DOM
    function setupDatePickerObserver() {
        if (typeof MutationObserver === 'undefined') return null;
        
        const observer = new MutationObserver(function(mutations) {
            let shouldConvert = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.classList && (
                                node.classList.contains('bootstrap-datetimepicker-widget') ||
                                node.classList.contains('datepicker-years') ||
                                node.classList.contains('datepicker-decades') ||
                                node.classList.contains('year') ||
                                node.classList.contains('decade')
                            )) {
                                shouldConvert = true;
                            }
                        }
                    });
                }
                
                // ตรวจสอบการเปลี่ยนแปลงข้อความ
                if (mutation.type === 'characterData' && 
                    mutation.target.parentElement &&
                    (mutation.target.parentElement.classList.contains('year') ||
                     mutation.target.parentElement.classList.contains('decade') ||
                     mutation.target.parentElement.classList.contains('picker-switch'))) {
                    shouldConvert = true;
                }
            });
            
            if (shouldConvert) {
                setTimeout(convertAllDatePickerYears, 50);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        return observer;
    }
    
    // Export functions สำหรับใช้งานภายนอก
    if (typeof window !== 'undefined') {
        window.convertAllDatePickerYears = convertAllDatePickerYears;
        window.resetDatePickerConversion = resetDatePickerConversion;
        window.initBuddhistDatePicker = initBuddhistDatePicker;
        window.setupDatePickerObserver = setupDatePickerObserver;

        // Auto-start เมื่อ DOM ready
        function autoStartBuddhistDatePicker() {
            // รอให้ jQuery และ moment.js โหลดเสร็จ
            setTimeout(function() {
                // เริ่มต้น MutationObserver
                setupDatePickerObserver();

                // แปลง elements ที่มีอยู่แล้ว
                convertAllDatePickerYears();

                // ตั้งค่า global event listeners สำหรับ datepicker ทั้งหมด
                if (typeof $ !== 'undefined') {
                    // Event สำหรับการแสดงผล
                    $(document).on('dp.show', function() {
                        setTimeout(convertAllDatePickerYears, 100);
                        setTimeout(convertAllDatePickerYears, 300);
                    });

                    // Event สำหรับการอัพเดท
                    $(document).on('dp.update', function() {
                        setTimeout(convertAllDatePickerYears, 100);
                        setTimeout(convertAllDatePickerYears, 300);
                    });

                    // Event สำหรับการเปลี่ยน view mode - สำคัญมาก!
                    $(document).on('dp.viewModeChange', function() {
                        setTimeout(function() {
                            resetDatePickerConversion();
                            convertAllDatePickerYears();
                        }, 50);
                        setTimeout(convertAllDatePickerYears, 200);
                        setTimeout(convertAllDatePickerYears, 400);
                    });

                    // Event สำหรับการเปลี่ยนแปลงวันที่
                    $(document).on('dp.change', function() {
                        setTimeout(convertAllDatePickerYears, 100);
                    });

                    // ไม่รีเซ็ตเมื่อ hide เพื่อป้องกันปัญหา
                    // $(document).on('dp.hide', function() {
                    //     resetDatePickerConversion();
                    // });
                }

                console.log('[Buddhist DatePicker] Auto-started successfully');
            }, 1000); // รอ 1 วินาทีให้ libraries โหลดเสร็จ
        }

        // เริ่มต้นเมื่อ DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', autoStartBuddhistDatePicker);
        } else {
            // DOM ready แล้ว
            autoStartBuddhistDatePicker();
        }
    }

});