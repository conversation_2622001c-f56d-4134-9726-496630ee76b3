# Buddhist Date Picker Solutions

โซลูชันสำหรับแก้ปัญหาการแปลงปี พ.ศ. ใน Date Picker ทุก mode (days, months, years)

## ปัญหาที่แก้ไข

ปัญหาเดิม: ฟังก์ชัน `postformat` ใน moment.js locale จะแปลงปี ค.ศ. เป็น พ.ศ. เฉพาะตอนเปิด picker ครั้งแรกเท่านั้น เมื่อเปลี่ยนไปยัง mode อื่นๆ (month picker หรือ year picker) title และเนื้อหาภายในจะไม่ถูกแปลงเป็น พ.ศ.

## โซลูชันที่มี

### 1. Buddhist Date Picker Component (React)
Component ที่สร้างขึ้นใหม่สำหรับ React ที่รองรับ พ.ศ. ครบทุก mode

**ไฟล์:** `src/components/ui/buddhist-date-picker.tsx`

**การใช้งาน:**
```tsx
import { BuddhistDatePicker } from "@/components/ui/buddhist-date-picker";

function MyComponent() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();

  return (
    <BuddhistDatePicker
      value={selectedDate}
      onChange={setSelectedDate}
      placeholder="เลือกวันที่"
      format="DD/MM/YYYY"
    />
  );
}
```

### 2. Buddhist Date Utils
ฟังก์ชันช่วยเหลือสำหรับการจัดการวันที่ พ.ศ.

**ไฟล์:** `src/lib/buddhist-date-utils.ts`

**ฟังก์ชันหลัก:**
```typescript
// แปลงปี
toBuddhistYear(2024) // → 2567
toGregorianYear(2567) // → 2024

// ฟอร์แมตวันที่
formatBuddhistDate(new Date(), 'DD/MM/YYYY') // → "25/09/2567"
formatThaiDate(new Date(), true) // → "วันพุธที่ 25 กันยายน 2567"

// แปลงสตริงเป็นวันที่
parseBuddhistDate("25/09/2567") // → Date object
```

### 3. Buddhist DatePicker Enhancer
โซลูชันสำหรับแก้ไข date picker ที่มีอยู่แล้วโดยใช้ DOM manipulation

**ไฟล์:** `src/lib/buddhist-datepicker-enhancer.ts`

**การใช้งาน:**
```typescript
import { initBuddhistDatePickerEnhancer } from "@/lib/buddhist-datepicker-enhancer";

// เริ่มต้นการแปลงอัตโนมัติ
initBuddhistDatePickerEnhancer({
  debug: true, // เปิด debug mode
  delay: 50,   // ความล่าช้าในการแปลง (ms)
});
```

### 4. PostFormat Buddhist Fixer
โซลูชันเฉพาะสำหรับแก้ปัญหา postformat ใน moment.js และ jQuery Bootstrap DateTimePicker

**ไฟล์:** `src/lib/fix-postformat-buddhist.ts`

**การใช้งาน:**
```typescript
import { initPostFormatBuddhistFixer } from "@/lib/fix-postformat-buddhist";

// แก้ไขปัญหา postformat
initPostFormatBuddhistFixer({
  debug: true
});
```

## วิธีการแก้ปัญหาเดิม

### สำหรับ jQuery Bootstrap DateTimePicker

1. **ใช้ PostFormat Buddhist Fixer (แนะนำ)**
```javascript
// เพิ่มใน HTML หรือ main.js
import { initPostFormatBuddhistFixer } from "@/lib/fix-postformat-buddhist";
initPostFormatBuddhistFixer({ debug: false });
```

2. **ใช้ moment.js locale ที่แก้ไขแล้ว**
```javascript
moment.defineLocale('th-buddhist-fixed', {
  // ... (ดูใน fix-postformat-buddhist.ts)
  postformat: function (string) {
    return string.replace(/\b(\d{4})\b/g, function (match, year) {
      const yearNum = parseInt(year, 10);
      // แปลงเฉพาะปี ค.ศ. (1900-2100) เป็น พ.ศ.
      if (yearNum >= 1900 && yearNum <= 2100) {
        return yearNum + 543;
      }
      // ถ้าเป็น พ.ศ. อยู่แล้ว ไม่ต้องแปลง
      return match;
    });
  }
});

// ใช้ locale ใหม่
$('#datepicker').datetimepicker({
  locale: 'th-buddhist-fixed',
  format: 'DD/MM/YYYY'
});
```

3. **ใช้ Event Listeners**
```javascript
$('#datepicker').on('dp.show dp.update dp.viewModeChange', function() {
  setTimeout(function() {
    $('.bootstrap-datetimepicker-widget').find('.year, .picker-switch, th').each(function() {
      const $this = $(this);
      if (!$this.data('buddhist-converted')) {
        const text = $this.text();
        const converted = text.replace(/\b(\d{4})\b/g, function(match, year) {
          const yearNum = parseInt(year, 10);
          if (yearNum >= 1900 && yearNum <= 2100) {
            return yearNum + 543;
          }
          return match;
        });
        $this.text(converted);
        $this.data('buddhist-converted', true);
      }
    });
  }, 50);
});
```

### สำหรับ React Applications

1. **ใช้ Buddhist Date Picker Component (แนะนำ)**
```tsx
import { BuddhistDatePicker } from "@/components/ui/buddhist-date-picker";
// ใช้แทน date picker เดิม
```

2. **ใช้ Buddhist DatePicker Enhancer**
```typescript
// ใน useEffect หรือ main.ts
import { initBuddhistDatePickerEnhancer } from "@/lib/buddhist-datepicker-enhancer";

useEffect(() => {
  const enhancer = initBuddhistDatePickerEnhancer();
  return () => enhancer.stop();
}, []);
```

## การทดสอบ

เข้าไปที่หน้าทดสอบ: `/test-buddhist-datepicker`

หน้านี้จะมี:
- Buddhist Date Picker Component
- การควบคุม Enhancer
- ฟังก์ชันทดสอบต่างๆ
- ตัวอย่าง elements ที่จะถูกแปลง

## Features

### Buddhist Date Picker Component
- ✅ รองรับ พ.ศ. ทุก mode (days, months, years)
- ✅ แสดงชื่อเดือนภาษาไทย
- ✅ Navigation ระหว่าง modes
- ✅ Responsive design
- ✅ Keyboard accessible
- ✅ TypeScript support

### Buddhist DatePicker Enhancer
- ✅ แปลงปี ค.ศ. เป็น พ.ศ. อัตโนมัติ
- ✅ รองรับ date picker หลายประเภท
- ✅ ใช้ MutationObserver ตรวจจับการเปลี่ยนแปลง
- ✅ ป้องกันการแปลงซ้ำ
- ✅ Debug mode

### PostFormat Buddhist Fixer
- ✅ แก้ปัญหา postformat เฉพาะ
- ✅ รองรับ moment.js locale
- ✅ Event listeners สำหรับ Bootstrap DateTimePicker
- ✅ Auto-start เมื่อ DOM ready

## Browser Support

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## Dependencies

### Buddhist Date Picker Component
- React 18+
- Lucide React (icons)
- Tailwind CSS

### Enhancer & Fixer
- ไม่มี dependencies (vanilla JavaScript)
- รองรับ jQuery (optional)
- รองรับ moment.js (optional)

## การติดตั้ง

ไฟล์ทั้งหมดถูกสร้างขึ้นแล้วใน project นี้ สามารถใช้งานได้ทันที

## การ Customize

### เพิ่ม Selectors ใหม่
```typescript
initBuddhistDatePickerEnhancer({
  selectors: [
    '.my-custom-datepicker .year',
    '.my-custom-datepicker .title'
  ]
});
```

### เปลี่ยนรูปแบบวันที่
```typescript
formatBuddhistDate(date, 'DD/MM/YY') // → "25/09/67"
formatBuddhistDate(date, 'YYYY-MM-DD') // → "2567-09-25"
```

### Custom Logger
```typescript
initPostFormatBuddhistFixer({
  debug: true,
  logger: (message, data) => {
    console.log(`[Custom] ${message}`, data);
  }
});
```

## Troubleshooting

### ปัญหา: ปีไม่ถูกแปลง
- ตรวจสอบว่า enhancer ทำงานอยู่หรือไม่
- เปิด debug mode เพื่อดู logs
- ตรวจสอบ selectors ว่าตรงกับ elements หรือไม่

### ปัญหา: แปลงซ้ำ
- Enhancer มีการป้องกันการแปลงซ้ำอยู่แล้ว
- ถ้ายังมีปัญหา ให้ reset การแปลง

### ปัญหา: moment.js ไม่ทำงาน
- ตรวจสอบว่า moment.js โหลดเสร็จแล้วหรือไม่
- เพิ่ม delay ในการเริ่มต้น fixer

## License

MIT License
