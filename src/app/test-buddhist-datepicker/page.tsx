"use client";

import { useState, useEffect } from "react";
import { BuddhistDatePicker } from "@/components/ui/buddhist-date-picker";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  formatBuddhistDate, 
  formatThaiDate, 
  formatThaiDateTime,
  parseBuddhistDate,
  toBuddhistYear,
  toGregorianYear
} from "@/lib/buddhist-date-utils";
import { 
  initBuddhistDatePickerEnhancer, 
  stopBuddhistDatePickerEnhancer,
  convertAllDatePickerElements,
  resetAllDatePickerConversions
} from "@/lib/buddhist-datepicker-enhancer";

export default function TestBuddhistDatePickerPage() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [enhancerActive, setEnhancerActive] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    // เริ่มต้น enhancer
    initBuddhistDatePickerEnhancer({ debug: true });
    setEnhancerActive(true);

    return () => {
      stopBuddhistDatePickerEnhancer();
    };
  }, []);

  const handleDateChange = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      const result = `Selected: ${formatBuddhistDate(date)} (${formatThaiDate(date)})`;
      setTestResults(prev => [result, ...prev.slice(0, 9)]);
    }
  };

  const toggleEnhancer = () => {
    if (enhancerActive) {
      stopBuddhistDatePickerEnhancer();
      setEnhancerActive(false);
    } else {
      initBuddhistDatePickerEnhancer({ debug: true });
      setEnhancerActive(true);
    }
  };

  const testDateConversions = () => {
    const testDates = [
      new Date(2024, 0, 1), // 1 มกราคม 2567
      new Date(2023, 11, 31), // 31 ธันวาคม 2566
      new Date(2025, 5, 15), // 15 มิถุนายน 2568
    ];

    const results = testDates.map(date => {
      const buddhist = formatBuddhistDate(date);
      const thai = formatThaiDate(date, true);
      const thaiDateTime = formatThaiDateTime(date, true);
      return `${buddhist} = ${thai} = ${thaiDateTime}`;
    });

    setTestResults(results);
  };

  const testYearConversions = () => {
    const testYears = [2020, 2021, 2022, 2023, 2024, 2025];
    const results = testYears.map(year => {
      const buddhist = toBuddhistYear(year);
      const backToGregorian = toGregorianYear(buddhist);
      return `${year} ค.ศ. = ${buddhist} พ.ศ. = ${backToGregorian} ค.ศ.`;
    });

    setTestResults(results);
  };

  const testDateParsing = () => {
    const testStrings = [
      "01/01/2567",
      "15/06/2568",
      "31/12/2566",
      "01/01/67", // ปี 2 หลัก
    ];

    const results = testStrings.map(dateStr => {
      const parsed = parseBuddhistDate(dateStr);
      if (parsed) {
        const formatted = formatBuddhistDate(parsed);
        return `"${dateStr}" → ${formatted} (${formatThaiDate(parsed)})`;
      } else {
        return `"${dateStr}" → Invalid date`;
      }
    });

    setTestResults(results);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Buddhist Date Picker Test</h1>
        <p className="text-gray-600">ทดสอบการทำงานของ Date Picker พ.ศ.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Date Picker Test */}
        <Card>
          <CardHeader>
            <CardTitle>Buddhist Date Picker</CardTitle>
            <CardDescription>
              ทดสอบการเลือกวันที่ด้วย Buddhist Date Picker
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">เลือกวันที่:</label>
              <BuddhistDatePicker
                value={selectedDate}
                onChange={handleDateChange}
                placeholder="เลือกวันที่"
                className="w-full"
              />
            </div>
            
            {selectedDate && (
              <div className="p-3 bg-gray-50 rounded-md">
                <p className="text-sm"><strong>วันที่ที่เลือก:</strong></p>
                <p className="text-sm">รูปแบบ DD/MM/YYYY: {formatBuddhistDate(selectedDate)}</p>
                <p className="text-sm">รูปแบบไทย: {formatThaiDate(selectedDate, true)}</p>
                <p className="text-sm">รูปแบบเต็ม: {formatThaiDateTime(selectedDate, true)}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhancer Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Date Picker Enhancer</CardTitle>
            <CardDescription>
              ควบคุมการทำงานของ Buddhist Date Picker Enhancer
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Enhancer Status:</span>
              <span className={`text-sm font-medium ${enhancerActive ? 'text-green-600' : 'text-red-600'}`}>
                {enhancerActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="space-y-2">
              <Button 
                onClick={toggleEnhancer}
                variant={enhancerActive ? "destructive" : "default"}
                className="w-full"
              >
                {enhancerActive ? 'Stop Enhancer' : 'Start Enhancer'}
              </Button>

              <Button 
                onClick={convertAllDatePickerElements}
                variant="outline"
                className="w-full"
                disabled={!enhancerActive}
              >
                Convert All Elements
              </Button>

              <Button 
                onClick={resetAllDatePickerConversions}
                variant="outline"
                className="w-full"
                disabled={!enhancerActive}
              >
                Reset All Conversions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Functions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Functions</CardTitle>
          <CardDescription>
            ทดสอบฟังก์ชันต่างๆ ของ Buddhist Date Utils
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <Button onClick={testDateConversions} variant="outline">
              Test Date Formatting
            </Button>
            <Button onClick={testYearConversions} variant="outline">
              Test Year Conversions
            </Button>
            <Button onClick={testDateParsing} variant="outline">
              Test Date Parsing
            </Button>
          </div>

          {testResults.length > 0 && (
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="font-medium mb-2">Test Results:</h4>
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <p key={index} className="text-sm font-mono">{result}</p>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Demo Date Picker Elements */}
      <Card>
        <CardHeader>
          <CardTitle>Demo Date Picker Elements</CardTitle>
          <CardDescription>
            ตัวอย่าง elements ที่จะถูกแปลงโดย Enhancer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Simulate Bootstrap DateTimePicker elements */}
            <div className="p-4 border rounded-md">
              <h5 className="font-medium mb-2">Simulated Bootstrap DateTimePicker:</h5>
              <div className="bootstrap-datetimepicker-widget">
                <div className="picker-switch">มกราคม 2024</div>
                <div className="year">2024</div>
                <div className="decade">2020-2029</div>
                <th>2024</th>
              </div>
            </div>

            {/* Generic date picker elements */}
            <div className="p-4 border rounded-md">
              <h5 className="font-medium mb-2">Generic Date Picker Elements:</h5>
              <div className="datepicker-years">
                <div className="year">2023</div>
                <div className="year">2024</div>
                <div className="year">2025</div>
              </div>
              <div className="datepicker-decades">
                <div className="decade">2020-2029</div>
              </div>
            </div>

            <Button 
              onClick={() => {
                // Force re-conversion of demo elements
                setTimeout(() => convertAllDatePickerElements(), 100);
              }}
              variant="outline"
            >
              Re-convert Demo Elements
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
