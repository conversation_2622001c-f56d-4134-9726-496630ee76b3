"use client";

import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { Calendar, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Input } from "./input";

interface BuddhistDatePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  format?: string;
}

type ViewMode = 'days' | 'months' | 'years';

const THAI_MONTHS = [
  'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
  'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
];

const THAI_MONTHS_SHORT = [
  'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
  'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
];

// แปลง ค.ศ. เป็น พ.ศ.
const toBuddhistYear = (gregorianYear: number): number => {
  return gregorianYear + 543;
};

// แปลง พ.ศ. เป็น ค.ศ.
const toGregorianYear = (buddhistYear: number): number => {
  return buddhistYear - 543;
};

// ฟอร์แมตวันที่เป็น พ.ศ.
const formatBuddhistDate = (date: Date, format: string = 'DD/MM/YYYY'): string => {
  if (!date) return '';
  
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const buddhistYear = toBuddhistYear(date.getFullYear());
  
  return format
    .replace('DD', day)
    .replace('MM', month)
    .replace('YYYY', buddhistYear.toString());
};

export const BuddhistDatePicker: React.FC<BuddhistDatePickerProps> = ({
  value,
  onChange,
  placeholder = "เลือกวันที่",
  className,
  disabled = false,
  format = 'DD/MM/YYYY'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('days');
  const [currentDate, setCurrentDate] = useState(value || new Date());
  const [inputValue, setInputValue] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  // อัพเดต input value เมื่อ value เปลี่ยน
  useEffect(() => {
    if (value) {
      setInputValue(formatBuddhistDate(value, format));
      setCurrentDate(value);
    } else {
      setInputValue('');
    }
  }, [value, format]);

  // ปิด picker เมื่อคลิกข้างนอก
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleDateSelect = (date: Date) => {
    onChange?.(date);
    setIsOpen(false);
    setViewMode('days');
  };

  const handleMonthSelect = (monthIndex: number) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(monthIndex);
    setCurrentDate(newDate);
    setViewMode('days');
  };

  const handleYearSelect = (year: number) => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(year);
    setCurrentDate(newDate);
    setViewMode('months');
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const navigateYear = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setFullYear(newDate.getFullYear() - 1);
    } else {
      newDate.setFullYear(newDate.getFullYear() + 1);
    }
    setCurrentDate(newDate);
  };

  const navigateDecade = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setFullYear(newDate.getFullYear() - 10);
    } else {
      newDate.setFullYear(newDate.getFullYear() + 10);
    }
    setCurrentDate(newDate);
  };

  const renderDaysView = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      days.push(date);
    }

    return (
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="ghost"
              onClick={() => setViewMode('months')}
              className="text-sm font-medium"
            >
              {THAI_MONTHS[month]}
            </Button>
            <Button
              variant="ghost"
              onClick={() => setViewMode('years')}
              className="text-sm font-medium"
            >
              {toBuddhistYear(year)}
            </Button>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Days grid */}
        <div className="grid grid-cols-7 gap-1">
          {['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'].map((day) => (
            <div key={day} className="h-8 flex items-center justify-center text-xs font-medium text-gray-500">
              {day}
            </div>
          ))}
          {days.map((date, index) => {
            const isCurrentMonth = date.getMonth() === month;
            const isSelected = value && 
              date.getDate() === value.getDate() &&
              date.getMonth() === value.getMonth() &&
              date.getFullYear() === value.getFullYear();
            const isToday = 
              date.getDate() === new Date().getDate() &&
              date.getMonth() === new Date().getMonth() &&
              date.getFullYear() === new Date().getFullYear();

            return (
              <Button
                key={index}
                variant={isSelected ? "default" : "ghost"}
                size="sm"
                onClick={() => handleDateSelect(date)}
                className={cn(
                  "h-8 w-8 p-0 text-xs",
                  !isCurrentMonth && "text-gray-400",
                  isToday && !isSelected && "bg-gray-100",
                  isSelected && "bg-blue-600 text-white hover:bg-blue-700"
                )}
              >
                {date.getDate()}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  const renderMonthsView = () => {
    const year = currentDate.getFullYear();

    return (
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateYear('prev')}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            onClick={() => setViewMode('years')}
            className="text-sm font-medium"
          >
            {toBuddhistYear(year)}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateYear('next')}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Months grid */}
        <div className="grid grid-cols-3 gap-2">
          {THAI_MONTHS_SHORT.map((month, index) => {
            const isSelected = value && 
              index === value.getMonth() &&
              year === value.getFullYear();

            return (
              <Button
                key={index}
                variant={isSelected ? "default" : "ghost"}
                onClick={() => handleMonthSelect(index)}
                className={cn(
                  "h-10 text-xs",
                  isSelected && "bg-blue-600 text-white hover:bg-blue-700"
                )}
              >
                {month}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  const renderYearsView = () => {
    const currentYear = currentDate.getFullYear();
    const startYear = Math.floor(currentYear / 10) * 10;
    const years = Array.from({ length: 12 }, (_, i) => startYear - 1 + i);

    return (
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDecade('prev')}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="text-sm font-medium">
            {toBuddhistYear(startYear)} - {toBuddhistYear(startYear + 9)}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDecade('next')}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Years grid */}
        <div className="grid grid-cols-3 gap-2">
          {years.map((year) => {
            const isSelected = value && year === value.getFullYear();
            const isInDecade = year >= startYear && year <= startYear + 9;

            return (
              <Button
                key={year}
                variant={isSelected ? "default" : "ghost"}
                onClick={() => handleYearSelect(year)}
                className={cn(
                  "h-10 text-xs",
                  !isInDecade && "text-gray-400",
                  isSelected && "bg-blue-600 text-white hover:bg-blue-700"
                )}
              >
                {toBuddhistYear(year)}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div ref={containerRef} className={cn("relative", className)}>
      <div className="relative">
        <Input
          value={inputValue}
          placeholder={placeholder}
          readOnly
          disabled={disabled}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className="pr-10 cursor-pointer"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
        >
          <Calendar className="h-4 w-4 text-gray-500" />
        </Button>
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg min-w-[280px]">
          {viewMode === 'days' && renderDaysView()}
          {viewMode === 'months' && renderMonthsView()}
          {viewMode === 'years' && renderYearsView()}
        </div>
      )}
    </div>
  );
};
