/**
 * แก้ปัญหา postformat ที่แปลงปี พ.ศ. ไม่ครบทุก mode
 * สำหรับ jQ<PERSON><PERSON> Bootstrap DateTimePicker และ moment.js
 */

import { toBuddhist<PERSON>ear, toG<PERSON>gorian<PERSON><PERSON>, isG<PERSON>gorian<PERSON>ear, isBuddhistYear } from './buddhist-date-utils';

declare global {
  interface Window {
    moment?: any;
    $?: any;
    jQuery?: any;
  }
}

interface PostFormatFixOptions {
  // เปิด/ปิด debug mode
  debug?: boolean;
  // ความล่าช้าในการแปลง (milliseconds)
  delay?: number;
  // ฟังก์ชันสำหรับ log
  logger?: (message: string, data?: any) => void;
}

class PostFormatBuddhistFixer {
  private options: Required<PostFormatFixOptions>;
  private observer: MutationObserver | null = null;
  private isActive = false;
  private eventListeners: Array<() => void> = [];

  constructor(options: PostFormatFixOptions = {}) {
    this.options = {
      debug: options.debug ?? false,
      delay: options.delay ?? 50,
      logger: options.logger ?? ((message: string, data?: any) => {
        if (this.options.debug) {
          console.log(`[PostFormatFixer] ${message}`, data || '');
        }
      })
    };
  }

  /**
   * เริ่มต้นการแก้ไข
   */
  public start(): void {
    if (this.isActive) {
      this.options.logger('Already active');
      return;
    }

    this.isActive = true;
    this.setupMomentLocale();
    this.setupDatePickerEvents();
    this.setupMutationObserver();
    this.convertExistingElements();
    this.options.logger('Started');
  }

  /**
   * หยุดการแก้ไข
   */
  public stop(): void {
    if (!this.isActive) {
      this.options.logger('Already stopped');
      return;
    }

    this.isActive = false;
    this.cleanup();
    this.options.logger('Stopped');
  }

  /**
   * ตั้งค่า moment.js locale ที่แก้ไขแล้ว
   */
  private setupMomentLocale(): void {
    if (typeof window === 'undefined' || !window.moment) {
      this.options.logger('Moment.js not found');
      return;
    }

    const moment = window.moment;

    // สร้าง locale ใหม่ที่แก้ไขปัญหา postformat
    moment.defineLocale('th-buddhist-fixed', {
      months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),
      monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),
      weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),
      weekdaysShort: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),
      weekdaysMin: 'อา_จ_อ_พ_พฤ_ศ_ส'.split('_'),
      longDateFormat: {
        LT: 'H:mm',
        LTS: 'H:mm:ss',
        L: 'DD/MM/YYYY',
        LL: 'D MMMM YYYY',
        LLL: 'D MMMM YYYY เวลา H:mm',
        LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'
      },
      // แปลง พ.ศ. กลับเป็น ค.ศ. เมื่อ parse
      preparse: (string: string) => {
        return string.replace(/\b(\d{4})\b/g, (match, year) => {
          const yearNum = parseInt(year, 10);
          if (isBuddhistYear(yearNum)) {
            return toGregorianYear(yearNum).toString();
          }
          return match;
        });
      },
      // แปลง ค.ศ. เป็น พ.ศ. เมื่อแสดงผล (แก้ไขให้ตรวจสอบก่อนแปลง)
      postformat: (string: string) => {
        return string.replace(/\b(\d{4})\b/g, (match, year) => {
          const yearNum = parseInt(year, 10);
          // แปลงเฉพาะปี ค.ศ. เป็น พ.ศ.
          if (isGregorianYear(yearNum)) {
            return toBuddhistYear(yearNum).toString();
          }
          // ถ้าเป็น พ.ศ. อยู่แล้ว ไม่ต้องแปลง
          return match;
        });
      }
    });

    this.options.logger('Moment.js locale setup complete');
  }

  /**
   * ตั้งค่า event listeners สำหรับ date picker
   */
  private setupDatePickerEvents(): void {
    if (typeof window === 'undefined' || !window.$) {
      this.options.logger('jQuery not found');
      return;
    }

    const $ = window.$;

    // ฟังก์ชันแปลงทุก elements ใน date picker
    const convertDatePickerElements = () => {
      setTimeout(() => {
        this.convertExistingElements();
      }, this.options.delay);
    };

    // Event listeners สำหรับ Bootstrap DateTimePicker
    const events = [
      'dp.show',      // เมื่อเปิด picker
      'dp.update',    // เมื่อมีการอัพเดท
      'dp.change',    // เมื่อเปลี่ยนวันที่
      'dp.viewModeChange', // เมื่อเปลี่ยน view mode (สำคัญ!)
    ];

    events.forEach(eventName => {
      $(document).on(eventName, '[data-toggle="datetimepicker"], .datetimepicker', (e: any) => {
        this.options.logger(`Event: ${eventName}`, e.target);
        convertDatePickerElements();
      });
    });

    // เก็บ cleanup functions
    this.eventListeners.push(() => {
      events.forEach(eventName => {
        $(document).off(eventName);
      });
    });

    this.options.logger('Date picker events setup complete');
  }

  /**
   * ตั้งค่า MutationObserver
   */
  private setupMutationObserver(): void {
    if (typeof MutationObserver === 'undefined') {
      this.options.logger('MutationObserver not supported');
      return;
    }

    this.observer = new MutationObserver((mutations) => {
      let shouldConvert = false;

      mutations.forEach((mutation) => {
        // ตรวจสอบ nodes ที่เพิ่มเข้ามา
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // ตรวจสอบว่าเป็น date picker widget หรือไม่
              if (this.isDatePickerWidget(element)) {
                shouldConvert = true;
                this.options.logger('Date picker widget added', element.className);
              }
            }
          });
        }

        // ตรวจสอบการเปลี่ยนแปลงข้อความ
        if (mutation.type === 'characterData') {
          const parentElement = mutation.target.parentElement;
          if (parentElement && this.isDatePickerElement(parentElement)) {
            shouldConvert = true;
            this.options.logger('Text content changed in date picker', parentElement.className);
          }
        }
      });

      if (shouldConvert) {
        setTimeout(() => this.convertExistingElements(), this.options.delay);
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true
    });

    this.options.logger('MutationObserver setup complete');
  }

  /**
   * แปลง elements ที่มีอยู่แล้ว
   */
  private convertExistingElements(): void {
    if (!this.isActive) return;

    const selectors = [
      // Bootstrap DateTimePicker selectors
      '.bootstrap-datetimepicker-widget .year',
      '.bootstrap-datetimepicker-widget .decade',
      '.bootstrap-datetimepicker-widget .picker-switch',
      '.bootstrap-datetimepicker-widget th',
      '.bootstrap-datetimepicker-widget .datepicker-years .year',
      '.bootstrap-datetimepicker-widget .datepicker-decades .decade',
      '.bootstrap-datetimepicker-widget .datepicker-months th',
      '.bootstrap-datetimepicker-widget .datepicker-days th',
      
      // Generic selectors
      '.datepicker-years .year',
      '.datepicker-decades .decade',
      '.datepicker-months th',
      '.datepicker-days th',
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => this.convertElement(element));
    });
  }

  /**
   * แปลง element เดียว
   */
  private convertElement(element: Element): void {
    if (!element || element.hasAttribute('data-buddhist-converted')) {
      return;
    }

    const originalText = element.textContent || '';
    if (!originalText.trim()) {
      return;
    }

    // แปลงปี
    const convertedText = this.convertYearsInText(originalText);
    
    if (convertedText !== originalText) {
      element.textContent = convertedText;
      element.setAttribute('data-buddhist-converted', 'true');
      element.setAttribute('data-buddhist-original', originalText);
      
      this.options.logger('Converted element', {
        selector: this.getElementSelector(element),
        original: originalText,
        converted: convertedText
      });
    }
  }

  /**
   * แปลงปีในข้อความ
   */
  private convertYearsInText(text: string): string {
    return text
      // แปลงปีเดี่ยว (เช่น 2024 → 2567)
      .replace(/\b(\d{4})\b/g, (match, year) => {
        const yearNum = parseInt(year, 10);
        if (isGregorianYear(yearNum)) {
          return toBuddhistYear(yearNum).toString();
        }
        return match;
      })
      // แปลงช่วงปี (เช่น 2020-2029 → 2563-2572)
      .replace(/(\d{4})\s*[-–—]\s*(\d{4})/g, (match, startYear, endYear) => {
        const start = parseInt(startYear, 10);
        const end = parseInt(endYear, 10);
        
        if (isGregorianYear(start) && isGregorianYear(end)) {
          return `${toBuddhistYear(start)}-${toBuddhistYear(end)}`;
        }
        return match;
      });
  }

  /**
   * ตรวจสอบว่า element เป็น date picker widget หรือไม่
   */
  private isDatePickerWidget(element: Element): boolean {
    const classNames = [
      'bootstrap-datetimepicker-widget',
      'datepicker-years',
      'datepicker-decades',
      'datepicker-months',
      'datepicker-days'
    ];

    return classNames.some(className => element.classList.contains(className));
  }

  /**
   * ตรวจสอบว่า element เป็นส่วนหนึ่งของ date picker หรือไม่
   */
  private isDatePickerElement(element: Element): boolean {
    return element.closest('.bootstrap-datetimepicker-widget') !== null ||
           element.closest('.datepicker-years') !== null ||
           element.closest('.datepicker-decades') !== null ||
           element.closest('.datepicker-months') !== null ||
           element.closest('.datepicker-days') !== null;
  }

  /**
   * สร้าง selector สำหรับ element (สำหรับ debug)
   */
  private getElementSelector(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      return `.${Array.from(element.classList).join('.')}`;
    }
    
    return element.tagName.toLowerCase();
  }

  /**
   * ทำความสะอาด
   */
  private cleanup(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    this.eventListeners.forEach(cleanup => cleanup());
    this.eventListeners = [];

    // รีเซ็ตการแปลง
    document.querySelectorAll('[data-buddhist-converted]').forEach(element => {
      const original = element.getAttribute('data-buddhist-original');
      if (original) {
        element.textContent = original;
      }
      element.removeAttribute('data-buddhist-converted');
      element.removeAttribute('data-buddhist-original');
    });
  }
}

// สร้าง instance เดียวสำหรับใช้งานทั่วไป
let globalFixer: PostFormatBuddhistFixer | null = null;

/**
 * เริ่มต้นการแก้ไข postformat
 */
export function initPostFormatBuddhistFixer(options?: PostFormatFixOptions): PostFormatBuddhistFixer {
  if (globalFixer) {
    globalFixer.stop();
  }
  
  globalFixer = new PostFormatBuddhistFixer(options);
  globalFixer.start();
  
  return globalFixer;
}

/**
 * หยุดการแก้ไข postformat
 */
export function stopPostFormatBuddhistFixer(): void {
  if (globalFixer) {
    globalFixer.stop();
    globalFixer = null;
  }
}

// Export class สำหรับใช้งานแบบ custom
export { PostFormatBuddhistFixer };

// Auto-start เมื่อ DOM ready (สำหรับ browser environment)
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // รอให้ moment.js และ jQuery โหลดเสร็จก่อน
      setTimeout(() => {
        initPostFormatBuddhistFixer({ debug: false });
      }, 1500);
    });
  } else {
    // DOM ready แล้ว
    setTimeout(() => {
      initPostFormatBuddhistFixer({ debug: false });
    }, 1500);
  }
}
