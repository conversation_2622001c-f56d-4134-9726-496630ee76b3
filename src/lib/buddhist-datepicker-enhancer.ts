/**
 * Buddhist DatePicker Enhancer
 * แก้ปัญหาการแปลงปี พ.ศ. ในทุก mode ของ date picker
 * รองรับ jQ<PERSON><PERSON> Bootstrap DateTimePicker และ date picker อื่นๆ
 */

import { toBuddhistYear, toGregorian<PERSON>ear, isGregorian<PERSON>ear, isBuddhistYear } from './buddhist-date-utils';

interface DatePickerEnhancerOptions {
  // Selectors สำหรับ elements ที่ต้องแปลงปี
  selectors?: string[];
  // ความล่าช้าในการแปลง (milliseconds)
  delay?: number;
  // เปิด/ปิด debug mode
  debug?: boolean;
  // ฟังก์ชันสำหรับ log
  logger?: (message: string, data?: any) => void;
}

class BuddhistDatePickerEnhancer {
  private options: Required<DatePickerEnhancerOptions>;
  private observer: MutationObserver | null = null;
  private isActive = false;
  private convertedElements = new WeakSet<Element>();

  constructor(options: DatePickerEnhancerOptions = {}) {
    this.options = {
      selectors: [
        // Bootstrap DateTimePicker
        '.bootstrap-datetimepicker-widget .year',
        '.bootstrap-datetimepicker-widget .decade',
        '.bootstrap-datetimepicker-widget .picker-switch',
        '.bootstrap-datetimepicker-widget th',
        '.bootstrap-datetimepicker-widget .datepicker-years .year',
        '.bootstrap-datetimepicker-widget .datepicker-decades .decade',
        
        // Generic date picker selectors
        '.datepicker-years .year',
        '.datepicker-decades .decade',
        '.datepicker-months th',
        '.datepicker-days th',
        '.year-picker .year',
        '.month-picker .year',
        '.date-picker-header',
        
        // Custom selectors
        '[data-year]',
        '[data-decade]',
        '.picker-title',
        '.calendar-header',
        ...options.selectors || []
      ],
      delay: options.delay ?? 50,
      debug: options.debug ?? false,
      logger: options.logger ?? ((message: string, data?: any) => {
        if (this.options.debug) {
          console.log(`[BuddhistDatePicker] ${message}`, data || '');
        }
      })
    };
  }

  /**
   * เริ่มต้นการทำงาน
   */
  public start(): void {
    if (this.isActive) {
      this.options.logger('Already active');
      return;
    }

    this.isActive = true;
    this.setupMutationObserver();
    this.convertAllExistingElements();
    this.options.logger('Started');
  }

  /**
   * หยุดการทำงาน
   */
  public stop(): void {
    if (!this.isActive) {
      this.options.logger('Already stopped');
      return;
    }

    this.isActive = false;
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.options.logger('Stopped');
  }

  /**
   * แปลงทุก elements ที่มีอยู่แล้ว
   */
  public convertAllExistingElements(): void {
    this.options.selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => this.convertElement(element));
    });
  }

  /**
   * รีเซ็ตการแปลงทั้งหมด
   */
  public resetAllConversions(): void {
    this.convertedElements = new WeakSet<Element>();
    document.querySelectorAll('[data-buddhist-original]').forEach(element => {
      const original = element.getAttribute('data-buddhist-original');
      if (original) {
        element.textContent = original;
        element.removeAttribute('data-buddhist-original');
        element.removeAttribute('data-buddhist-converted');
      }
    });
    this.options.logger('Reset all conversions');
  }

  /**
   * ตั้งค่า MutationObserver
   */
  private setupMutationObserver(): void {
    if (typeof MutationObserver === 'undefined') {
      this.options.logger('MutationObserver not supported');
      return;
    }

    this.observer = new MutationObserver((mutations) => {
      let shouldConvert = false;

      mutations.forEach((mutation) => {
        // ตรวจสอบ nodes ที่เพิ่มเข้ามา
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // ตรวจสอบว่าเป็น date picker widget หรือไม่
              if (this.isDatePickerElement(element)) {
                shouldConvert = true;
                this.options.logger('Date picker element added', element.className);
              }
              
              // ตรวจสอบ children ของ element ที่เพิ่มเข้ามา
              if (element.querySelector && this.hasDatePickerChildren(element)) {
                shouldConvert = true;
                this.options.logger('Element with date picker children added', element.className);
              }
            }
          });
        }

        // ตรวจสอบการเปลี่ยนแปลงข้อความ
        if (mutation.type === 'characterData') {
          const parentElement = mutation.target.parentElement;
          if (parentElement && this.shouldConvertElement(parentElement)) {
            shouldConvert = true;
            this.options.logger('Text content changed', parentElement.className);
          }
        }

        // ตรวจสอบการเปลี่ยนแปลง attributes
        if (mutation.type === 'attributes' && mutation.target.nodeType === Node.ELEMENT_NODE) {
          const element = mutation.target as Element;
          if (this.shouldConvertElement(element)) {
            shouldConvert = true;
            this.options.logger('Attributes changed', element.className);
          }
        }
      });

      if (shouldConvert) {
        setTimeout(() => this.convertAllExistingElements(), this.options.delay);
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true,
      attributeFilter: ['class', 'data-year', 'data-decade']
    });

    this.options.logger('MutationObserver setup complete');
  }

  /**
   * ตรวจสอบว่า element เป็น date picker หรือไม่
   */
  private isDatePickerElement(element: Element): boolean {
    const classNames = [
      'bootstrap-datetimepicker-widget',
      'datepicker-years',
      'datepicker-decades',
      'datepicker-months',
      'datepicker-days',
      'year-picker',
      'month-picker',
      'date-picker'
    ];

    return classNames.some(className => element.classList.contains(className));
  }

  /**
   * ตรวจสอบว่า element มี date picker children หรือไม่
   */
  private hasDatePickerChildren(element: Element): boolean {
    return this.options.selectors.some(selector => {
      try {
        return element.querySelector(selector) !== null;
      } catch (e) {
        return false;
      }
    });
  }

  /**
   * ตรวจสอบว่าควรแปลง element นี้หรือไม่
   */
  private shouldConvertElement(element: Element): boolean {
    return this.options.selectors.some(selector => {
      try {
        return element.matches(selector);
      } catch (e) {
        return false;
      }
    });
  }

  /**
   * แปลง element เดียว
   */
  private convertElement(element: Element): void {
    if (!element || this.convertedElements.has(element)) {
      return;
    }

    const originalText = element.textContent || '';
    if (!originalText.trim()) {
      return;
    }

    // เก็บข้อความเดิมไว้
    if (!element.hasAttribute('data-buddhist-original')) {
      element.setAttribute('data-buddhist-original', originalText);
    }

    // แปลงปี
    const convertedText = this.convertYearsInText(originalText);
    
    if (convertedText !== originalText) {
      element.textContent = convertedText;
      element.setAttribute('data-buddhist-converted', 'true');
      this.convertedElements.add(element);
      
      this.options.logger('Converted element', {
        selector: this.getElementSelector(element),
        original: originalText,
        converted: convertedText
      });
    }
  }

  /**
   * แปลงปีในข้อความ
   */
  private convertYearsInText(text: string): string {
    return text
      // แปลงปีเดี่ยว (เช่น 2024 → 2567)
      .replace(/\b(\d{4})\b/g, (match, year) => {
        const yearNum = parseInt(year, 10);
        if (isGregorianYear(yearNum)) {
          return toBuddhistYear(yearNum).toString();
        }
        return match;
      })
      // แปลงช่วงปี (เช่น 2020-2029 → 2563-2572)
      .replace(/(\d{4})\s*[-–—]\s*(\d{4})/g, (match, startYear, endYear) => {
        const start = parseInt(startYear, 10);
        const end = parseInt(endYear, 10);
        
        if (isGregorianYear(start) && isGregorianYear(end)) {
          return `${toBuddhistYear(start)}-${toBuddhistYear(end)}`;
        }
        return match;
      });
  }

  /**
   * สร้าง selector สำหรับ element (สำหรับ debug)
   */
  private getElementSelector(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      return `.${Array.from(element.classList).join('.')}`;
    }
    
    return element.tagName.toLowerCase();
  }
}

// สร้าง instance เดียวสำหรับใช้งานทั่วไป
let globalEnhancer: BuddhistDatePickerEnhancer | null = null;

/**
 * เริ่มต้นการแปลงปี พ.ศ. สำหรับ date picker ทั้งหมด
 */
export function initBuddhistDatePickerEnhancer(options?: DatePickerEnhancerOptions): BuddhistDatePickerEnhancer {
  if (globalEnhancer) {
    globalEnhancer.stop();
  }
  
  globalEnhancer = new BuddhistDatePickerEnhancer(options);
  globalEnhancer.start();
  
  return globalEnhancer;
}

/**
 * หยุดการแปลงปี พ.ศ.
 */
export function stopBuddhistDatePickerEnhancer(): void {
  if (globalEnhancer) {
    globalEnhancer.stop();
    globalEnhancer = null;
  }
}

/**
 * แปลงทุก elements ที่มีอยู่แล้ว
 */
export function convertAllDatePickerElements(): void {
  if (globalEnhancer) {
    globalEnhancer.convertAllExistingElements();
  }
}

/**
 * รีเซ็ตการแปลงทั้งหมด
 */
export function resetAllDatePickerConversions(): void {
  if (globalEnhancer) {
    globalEnhancer.resetAllConversions();
  }
}

// Export class สำหรับใช้งานแบบ custom
export { BuddhistDatePickerEnhancer };

// Auto-start เมื่อ DOM ready (สำหรับ browser environment)
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Auto-start หลังจาก DOM ready 1 วินาที (ให้เวลา date picker libraries โหลด)
      setTimeout(() => {
        initBuddhistDatePickerEnhancer({ debug: false });
      }, 1000);
    });
  } else {
    // DOM ready แล้ว
    setTimeout(() => {
      initBuddhistDatePickerEnhancer({ debug: false });
    }, 1000);
  }
}
