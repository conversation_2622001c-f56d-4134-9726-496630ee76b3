/**
 * Utility functions for Buddhist Era (BE) date handling
 * แปลงระหว่าง ค.ศ. และ พ.ศ.
 */

// แปลง ค.ศ. เป็น พ.ศ.
export const toBuddhistYear = (gregorianYear: number): number => {
  return gregorianYear + 543;
};

// แปลง พ.ศ. เป็น ค.ศ.
export const toGregorianYear = (buddhistYear: number): number => {
  return buddhistYear - 543;
};

// ตรวจสอบว่าเป็นปี พ.ศ. หรือไม่
export const isBuddhistYear = (year: number): boolean => {
  return year >= 2443 && year <= 2643; // ช่วงปี พ.ศ. ที่สมเหตุสมผล (1900-2100 ค.ศ.)
};

// ตรวจสอบว่าเป็นปี ค.ศ. หรือไม่
export const isGregorianYear = (year: number): boolean => {
  return year >= 1900 && year <= 2100; // ช่วงปี ค.ศ. ที่สมเหตุสมผล
};

// ฟอร์แมตวันที่เป็น พ.ศ.
export const formatBuddhistDate = (date: Date, format: string = 'DD/MM/YYYY'): string => {
  if (!date || isNaN(date.getTime())) return '';
  
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const buddhistYear = toBuddhistYear(year);
  
  return format
    .replace('DD', day)
    .replace('MM', month)
    .replace('YYYY', buddhistYear.toString())
    .replace('YY', buddhistYear.toString().slice(-2));
};

// แปลงสตริงวันที่ พ.ศ. เป็น Date object
export const parseBuddhistDate = (dateString: string, format: string = 'DD/MM/YYYY'): Date | null => {
  if (!dateString) return null;
  
  try {
    // รองรับรูปแบบ DD/MM/YYYY หรือ DD/MM/YY
    const parts = dateString.split('/');
    if (parts.length !== 3) return null;
    
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // JavaScript month is 0-based
    let year = parseInt(parts[2], 10);
    
    // ถ้าเป็นปี 2 หลัก ให้แปลงเป็น 4 หลัก
    if (year < 100) {
      year += 2500; // สมมติว่าเป็น พ.ศ.
    }
    
    // ถ้าเป็น พ.ศ. ให้แปลงเป็น ค.ศ.
    if (isBuddhistYear(year)) {
      year = toGregorianYear(year);
    }
    
    const date = new Date(year, month, day);
    
    // ตรวจสอบว่าวันที่ถูกต้องหรือไม่
    if (date.getFullYear() !== year || date.getMonth() !== month || date.getDate() !== day) {
      return null;
    }
    
    return date;
  } catch (error) {
    return null;
  }
};

// แปลงสตริงที่มีปีเป็น พ.ศ.
export const convertStringToBuddhistYear = (text: string): string => {
  return text.replace(/\b(\d{4})\b/g, (match, year) => {
    const yearNum = parseInt(year, 10);
    if (isGregorianYear(yearNum)) {
      return toBuddhistYear(yearNum).toString();
    }
    return match;
  });
};

// แปลงสตริงที่มีปีเป็น ค.ศ.
export const convertStringToGregorianYear = (text: string): string => {
  return text.replace(/\b(\d{4})\b/g, (match, year) => {
    const yearNum = parseInt(year, 10);
    if (isBuddhistYear(yearNum)) {
      return toGregorianYear(yearNum).toString();
    }
    return match;
  });
};

// ชื่อเดือนภาษาไทย
export const THAI_MONTHS = [
  'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
  'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
];

export const THAI_MONTHS_SHORT = [
  'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
  'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
];

export const THAI_DAYS = [
  'อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'
];

export const THAI_DAYS_SHORT = [
  'อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'
];

// ฟอร์แมตวันที่แบบเต็มภาษาไทย
export const formatThaiDate = (date: Date, includeDayName: boolean = false): string => {
  if (!date || isNaN(date.getTime())) return '';
  
  const day = date.getDate();
  const month = THAI_MONTHS[date.getMonth()];
  const year = toBuddhistYear(date.getFullYear());
  const dayName = includeDayName ? `วัน${THAI_DAYS[date.getDay()]}ที่ ` : '';
  
  return `${dayName}${day} ${month} ${year}`;
};

// ฟอร์แมตวันที่และเวลาภาษาไทย
export const formatThaiDateTime = (date: Date, includeDayName: boolean = false): string => {
  if (!date || isNaN(date.getTime())) return '';
  
  const dateStr = formatThaiDate(date, includeDayName);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${dateStr} เวลา ${hours}:${minutes} น.`;
};

// ฟังก์ชันสำหรับแปลงปีในข้อความ (สำหรับใช้กับ postformat)
export const createBuddhistPostFormat = () => {
  return function(string: string): string {
    return string.replace(/\b(\d{4})\b/g, function(match, year) {
      const yearNum = parseInt(year, 10);
      // แปลงเฉพาะปี ค.ศ. (1900-2100) เป็น พ.ศ.
      if (isGregorianYear(yearNum)) {
        return toBuddhistYear(yearNum).toString();
      }
      // ถ้าเป็น พ.ศ. อยู่แล้ว ไม่ต้องแปลง
      return match;
    });
  };
};

// ฟังก์ชันสำหรับแปลงปีในข้อความกลับ (สำหรับใช้กับ preparse)
export const createBuddhistPreParse = () => {
  return function(string: string): string {
    return string.replace(/\b(\d{4})\b/g, function(match, year) {
      const yearNum = parseInt(year, 10);
      // แปลง พ.ศ. (2443-2643) กลับเป็น ค.ศ.
      if (isBuddhistYear(yearNum)) {
        return toGregorianYear(yearNum).toString();
      }
      return match;
    });
  };
};

// ฟังก์ชันสำหรับใช้กับ moment.js locale
export const createBuddhistMomentLocale = () => {
  return {
    months: THAI_MONTHS,
    monthsShort: THAI_MONTHS_SHORT,
    weekdays: THAI_DAYS,
    weekdaysShort: THAI_DAYS_SHORT,
    weekdaysMin: THAI_DAYS_SHORT,
    longDateFormat: {
      LT: 'H:mm',
      LTS: 'H:mm:ss',
      L: 'DD/MM/YYYY',
      LL: 'D MMMM YYYY',
      LLL: 'D MMMM YYYY เวลา H:mm',
      LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'
    },
    preparse: createBuddhistPreParse(),
    postformat: createBuddhistPostFormat()
  };
};
