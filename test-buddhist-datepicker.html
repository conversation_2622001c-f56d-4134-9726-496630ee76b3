<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Buddhist DatePicker</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap DateTimePicker CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">ทดสอบ Buddhist DatePicker (พ.ศ.)</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>DatePicker ปกติ</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="date1">เลือกวันที่:</label>
                            <div class="input-group date" id="datetimepicker1">
                                <input type="text" class="form-control" id="date1" />
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                        
                        <div class="form-group mt-3">
                            <label for="date2">เลือกเดือน/ปี:</label>
                            <div class="input-group date" id="datetimepicker2">
                                <input type="text" class="form-control" id="date2" />
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>ผลลัพธ์</h5>
                    </div>
                    <div class="card-body">
                        <div id="result1" class="mb-3">
                            <strong>วันที่ที่เลือก:</strong> <span id="selected-date">-</span>
                        </div>
                        <div id="result2" class="mb-3">
                            <strong>เดือน/ปีที่เลือก:</strong> <span id="selected-month">-</span>
                        </div>
                        
                        <hr>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary" onclick="testConversion()">
                                ทดสอบการแปลง
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetConversion()">
                                รีเซ็ต
                            </button>
                        </div>
                        
                        <div id="debug-info" class="small text-muted">
                            <strong>Debug Info:</strong><br>
                            <div id="debug-content">พร้อมทดสอบ...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>คำแนะนำการใช้งาน</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>คลิกที่ช่องวันที่เพื่อเปิด DatePicker</li>
                            <li>ตรวจสอบว่า title ด้านบนแสดงปี พ.ศ. หรือไม่</li>
                            <li>คลิกที่ title เพื่อเปลี่ยนไปยัง month view</li>
                            <li>ตรวจสอบว่า title ใน month view แสดงปี พ.ศ. หรือไม่</li>
                            <li>คลิกที่ปีเพื่อเปลี่ยนไปยัง year view</li>
                            <li>ตรวจสอบว่าปีทั้งหมดแสดงเป็น พ.ศ. หรือไม่</li>
                        </ol>
                        
                        <div class="alert alert-info mt-3">
                            <strong>หมายเหตุ:</strong> หากพบปัญหาการแปลง สามารถกดปุ่ม "ทดสอบการแปลง" เพื่อบังคับแปลงใหม่
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    
    <!-- Bootstrap DateTimePicker -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
    
    <!-- Buddhist Locale -->
    <script src="th-buddhist-locale.js"></script>
    
    <script>
        $(document).ready(function() {
            // ตั้งค่า locale เป็นภาษาไทย
            moment.locale('th');
            
            // สร้าง DatePicker สำหรับเลือกวันที่
            $('#datetimepicker1').datetimepicker({
                format: 'DD/MM/YYYY',
                locale: 'th',
                defaultDate: moment()
            });
            
            // สร้าง DatePicker สำหรับเลือกเดือน/ปี
            $('#datetimepicker2').datetimepicker({
                format: 'MM/YYYY',
                locale: 'th',
                viewMode: 'months',
                defaultDate: moment()
            });
            
            // Event handlers สำหรับแสดงผลลัพธ์
            $('#datetimepicker1').on('dp.change', function(e) {
                if (e.date) {
                    $('#selected-date').text(e.date.format('DD/MM/YYYY'));
                    updateDebugInfo('Date changed: ' + e.date.format('DD/MM/YYYY'));
                }
            });
            
            $('#datetimepicker2').on('dp.change', function(e) {
                if (e.date) {
                    $('#selected-month').text(e.date.format('MM/YYYY'));
                    updateDebugInfo('Month changed: ' + e.date.format('MM/YYYY'));
                }
            });
            
            // Debug events
            $(document).on('dp.show', function() {
                updateDebugInfo('DatePicker opened');
            });
            
            $(document).on('dp.viewModeChange', function() {
                updateDebugInfo('View mode changed');
            });
            
            updateDebugInfo('DatePickers initialized successfully');
        });
        
        function testConversion() {
            if (typeof convertAllDatePickerYears === 'function') {
                convertAllDatePickerYears();
                updateDebugInfo('Manual conversion triggered');
            } else {
                updateDebugInfo('ERROR: convertAllDatePickerYears function not found');
            }
        }
        
        function resetConversion() {
            if (typeof resetDatePickerConversion === 'function') {
                resetDatePickerConversion();
                updateDebugInfo('Conversion reset');
            } else {
                updateDebugInfo('ERROR: resetDatePickerConversion function not found');
            }
        }
        
        function updateDebugInfo(message) {
            const now = new Date().toLocaleTimeString();
            const debugContent = document.getElementById('debug-content');
            debugContent.innerHTML = `[${now}] ${message}<br>` + debugContent.innerHTML;
        }
    </script>
</body>
</html>
