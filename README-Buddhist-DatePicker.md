# Buddhist DatePicker (พ.ศ.) - Thai Locale

ไฟล์ locale สำหรับแปลงปี ค.ศ. เป็น พ.ศ. ใน DatePicker widgets โดยอัตโนมัติ

## ปัญหาที่แก้ไข

### ปัญหาเดิม
- DatePicker แสดงปี ค.ศ. ในส่วน title และ body ของ picker
- เมื่อเปลี่ยน view mode (เช่น จาก day view เป็น month view) title ยังคงแสดงปี ค.ศ.
- การแปลงไม่ครอบคลุมทุกส่วนของ widget

### การแก้ไข
1. **เพิ่ม selectors ให้ครอบคลุมมากขึ้น** - รวม title elements ทุกประเภท
2. **ปรับปรุง timing ของการแปลง** - เพิ่ม delay และแปลงหลายครั้ง
3. **เพิ่มฟังก์ชันเฉพาะสำหรับ title** - `convertDatePickerTitles()`
4. **ปรับปรุง event handling** - จัดการ `dp.viewModeChange` ให้ดีขึ้น

## ฟีเจอร์ใหม่

### ฟังก์ชันที่เพิ่มขึ้น
- `convertDatePickerTitles()` - แปลงเฉพาะ title elements
- การ export ฟังก์ชันเพิ่มเติมสำหรับใช้งานภายนอก
- การจัดการ event ที่ดีขึ้น

### Selectors ที่เพิ่มขึ้น
```javascript
// Title selectors ที่เพิ่มขึ้น
'.bootstrap-datetimepicker-widget th.picker-switch',
'.bootstrap-datetimepicker-widget .datepicker-switch',
'.bootstrap-datetimepicker-widget .datepicker-months .picker-switch',
'.bootstrap-datetimepicker-widget .datepicker-days .picker-switch',
'th.datepicker-switch',
'thead th.picker-switch',
'thead .picker-switch'
```

## การใช้งาน

### 1. การใช้งานพื้นฐาน
```html
<!-- รวมไฟล์ locale -->
<script src="th-buddhist-locale.js"></script>

<script>
$(document).ready(function() {
    // ตั้งค่า locale เป็นภาษาไทย
    moment.locale('th');
    
    // สร้าง DatePicker
    $('#datepicker').datetimepicker({
        format: 'DD/MM/YYYY',
        locale: 'th'
    });
});
</script>
```

### 2. การใช้งานขั้นสูง
```javascript
// ใช้ฟังก์ชันที่ export ออกมา
initBuddhistDatePicker('#datepicker', {
    format: 'DD/MM/YYYY',
    locale: 'th'
});

// แปลงด้วยตนเอง
convertAllDatePickerYears();

// แปลงเฉพาะ title
convertDatePickerTitles();

// รีเซ็ตการแปลง
resetDatePickerConversion();
```

## การทดสอบ

เปิดไฟล์ `test-buddhist-datepicker.html` ในเบราว์เซอร์เพื่อทดสอบ:

1. คลิกที่ช่องวันที่เพื่อเปิด DatePicker
2. ตรวจสอบว่า title ด้านบนแสดงปี พ.ศ.
3. คลิกที่ title เพื่อเปลี่ยน view mode
4. ตรวจสอบว่า title ใน month/year view แสดงปี พ.ศ.
5. ใช้ปุ่ม "ทดสอบการแปลง" หากพบปัญหา

## การแก้ไขปัญหา

### หาก title ยังแสดงปี ค.ศ.
```javascript
// บังคับแปลงใหม่
setTimeout(function() {
    convertAllDatePickerYears();
}, 500);

// หรือแปลงเฉพาะ title
setTimeout(function() {
    convertDatePickerTitles();
}, 500);
```

### หากการแปลงไม่ทำงาน
1. ตรวจสอบว่า jQuery และ moment.js โหลดเสร็จแล้ว
2. ตรวจสอบ console สำหรับ error messages
3. ลองเรียก `convertAllDatePickerYears()` ด้วยตนเอง

### การ Debug
```javascript
// ดู elements ที่ถูกแปลงแล้ว
document.querySelectorAll('[data-buddhist-converted]');

// ดูข้อความเดิมก่อนแปลง
document.querySelectorAll('[data-buddhist-original]');
```

## การปรับแต่ง

### เพิ่ม selector ใหม่
```javascript
// แก้ไขใน convertAllDatePickerYears()
const selectors = [
    // เพิ่ม selector ใหม่ที่นี่
    '.your-custom-selector',
    // ...
];
```

### ปรับ timing
```javascript
// แก้ไข delay ใน event handlers
setTimeout(convertAllDatePickerYears, 200); // เปลี่ยนจาก 100
```

## ข้อมูลเทคนิค

### Event Handlers
- `dp.show` - เมื่อเปิด DatePicker
- `dp.update` - เมื่อมีการอัพเดท
- `dp.viewModeChange` - เมื่อเปลี่ยน view mode (สำคัญมาก!)
- `dp.change` - เมื่อเปลี่ยนวันที่

### การทำงานของการแปลง
1. ตรวจสอบว่า element ถูกแปลงแล้วหรือไม่ (`data-buddhist-converted`)
2. เก็บข้อความเดิมไว้ (`data-buddhist-original`)
3. แปลงปี ค.ศ. (1900-2100) เป็น พ.ศ. (+543)
4. อัพเดทข้อความและทำเครื่องหมายว่าแปลงแล้ว

### Browser Support
- Chrome, Firefox, Safari, Edge
- IE 11+ (ต้องมี polyfill สำหรับ `querySelectorAll`)

## License
MIT License - ใช้งานได้อย่างอิสระ
