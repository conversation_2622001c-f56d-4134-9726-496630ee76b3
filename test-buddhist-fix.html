<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Buddhist DatePicker Fix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap DateTimePicker CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datetimepicker@4.17.47/build/css/bootstrap-datetimepicker.min.css">
    
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Test Buddhist DatePicker Fix</h1>
        <p class="text-center text-muted">ทดสอบการแก้ไขปัญหา postformat ใน th-buddhist-locale.js</p>

        <!-- DatePicker Test -->
        <div class="test-section">
            <h3>DatePicker Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="datepicker1">Date Picker 1:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="datepicker1" placeholder="เลือกวันที่">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="datepicker2">Date Picker 2:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="datepicker2" placeholder="เลือกวันที่">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <p><strong>การทดสอบ:</strong></p>
                <ol>
                    <li>คลิกที่ date picker</li>
                    <li>ดูว่าปีแสดงเป็น พ.ศ. หรือไม่</li>
                    <li>คลิกที่หัวข้อเดือน/ปี เพื่อเปลี่ยนไป month picker</li>
                    <li>ตรวจสอบว่าปีใน title แสดงเป็น พ.ศ.</li>
                    <li>คลิกที่ปี เพื่อเปลี่ยนไป year picker</li>
                    <li>ตรวจสอบว่าปีทั้งหมดแสดงเป็น พ.ศ.</li>
                </ol>
            </div>
        </div>

        <!-- Test Elements -->
        <div class="test-section">
            <h3>Test Elements (จะถูกแปลงอัตโนมัติ)</h3>
            <div class="row">
                <div class="col-md-4">
                    <h5>Bootstrap DateTimePicker Elements</h5>
                    <div class="bootstrap-datetimepicker-widget" style="position: static; display: block; border: 1px solid #ccc; padding: 10px;">
                        <div class="picker-switch">มกราคม 2024</div>
                        <div class="year">2024</div>
                        <div class="decade">2020-2029</div>
                        <th>2024</th>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Year Picker Elements</h5>
                    <div class="datepicker-years" style="border: 1px solid #ccc; padding: 10px;">
                        <div class="year">2023</div>
                        <div class="year">2024</div>
                        <div class="year">2025</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>Decade Picker Elements</h5>
                    <div class="datepicker-decades" style="border: 1px solid #ccc; padding: 10px;">
                        <div class="decade">2010-2019</div>
                        <div class="decade">2020-2029</div>
                        <div class="decade">2030-2039</div>
                    </div>
                </div>
            </div>
            <button id="force-convert" class="btn btn-secondary mt-3">Force Convert Test Elements</button>
            <button id="reset-convert" class="btn btn-warning mt-3">Reset Conversions</button>
        </div>

        <!-- Manual Test -->
        <div class="test-section">
            <h3>Manual Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="year-input">ใส่ปี ค.ศ.:</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="year-input" value="2024">
                        <div class="input-group-append">
                            <button class="btn btn-primary" id="convert-year">แปลงเป็น พ.ศ.</button>
                        </div>
                    </div>
                    <div id="year-result" class="mt-2 text-info"></div>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info" id="test-functions">Test All Functions</button>
                    <div id="function-result" class="mt-2 text-success"></div>
                </div>
            </div>
        </div>

        <!-- Log Output -->
        <div class="test-section">
            <h3>Debug Log</h3>
            <div id="log-output" class="log-output">
                <div class="text-muted">Debug logs will appear here...</div>
            </div>
            <button id="clear-log" class="btn btn-sm btn-secondary mt-2">Clear Log</button>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Moment.js -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/locale/th.js"></script>
    
    <!-- Bootstrap DateTimePicker -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-datetimepicker@4.17.47/build/js/bootstrap-datetimepicker.min.js"></script>
    
    <!-- Font Awesome (for icons) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- Buddhist DatePicker Fix -->
    <script src="th-buddhist-locale.js"></script>

    <script>
        $(document).ready(function() {
            // Override console.log เพื่อแสดงใน log output
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                
                const logOutput = document.getElementById('log-output');
                const logEntry = document.createElement('div');
                logEntry.textContent = new Date().toLocaleTimeString() + ': ' + args.join(' ');
                logOutput.appendChild(logEntry);
                logOutput.scrollTop = logOutput.scrollHeight;
            };

            // ตั้งค่า DatePickers
            $('#datepicker1').datetimepicker({
                locale: 'th',
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                showClear: true
            });

            $('#datepicker2').datetimepicker({
                locale: 'th',
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                showClear: true,
                viewMode: 'months'
            });

            // Event handlers
            $('#force-convert').click(function() {
                if (typeof convertAllDatePickerYears !== 'undefined') {
                    convertAllDatePickerYears();
                    console.log('Force converted all elements');
                } else {
                    console.log('convertAllDatePickerYears function not found');
                }
            });

            $('#reset-convert').click(function() {
                if (typeof resetDatePickerConversion !== 'undefined') {
                    resetDatePickerConversion();
                    console.log('Reset all conversions');
                } else {
                    console.log('resetDatePickerConversion function not found');
                }
            });

            $('#convert-year').click(function() {
                const year = parseInt($('#year-input').val());
                if (year) {
                    const buddhist = year + 543;
                    $('#year-result').text(`${year} ค.ศ. = ${buddhist} พ.ศ.`);
                }
            });

            $('#test-functions').click(function() {
                const functions = [
                    'convertAllDatePickerYears',
                    'resetDatePickerConversion',
                    'initBuddhistDatePicker',
                    'setupDatePickerObserver'
                ];
                
                const results = functions.map(func => {
                    return `${func}: ${typeof window[func] !== 'undefined' ? '✅' : '❌'}`;
                });
                
                $('#function-result').html(results.join('<br>'));
            });

            $('#clear-log').click(function() {
                $('#log-output').html('<div class="text-muted">Debug logs will appear here...</div>');
            });

            // แสดงสถานะ libraries
            console.log('jQuery:', typeof $ !== 'undefined' ? '✅' : '❌');
            console.log('Moment.js:', typeof moment !== 'undefined' ? '✅' : '❌');
            console.log('Bootstrap DateTimePicker:', typeof $.fn.datetimepicker !== 'undefined' ? '✅' : '❌');
            
            // ทดสอบ functions
            setTimeout(function() {
                $('#test-functions').click();
            }, 2000);
        });
    </script>
</body>
</html>
